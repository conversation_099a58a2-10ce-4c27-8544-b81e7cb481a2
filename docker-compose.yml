services:
  perplexity-agent:
    build: .
    ports:
      - "8000:8000"  # FastAPI
      - "5900:5900"  # VNC
      - "6379:6379"  # Redis
    volumes:
      - ./chrome_profile:/app/chrome_profile:rw
      - ./logs:/app/logs:rw
    environment:
      - DISPLAY=:1
      - REDIS_URL=redis://localhost:6379
      - PYTHONPATH=/app
      # German localization
      - LOCALE=de-DE
      - TIMEZONE=Europe/Berlin
      - GEOLOCATION_LAT=50.5840
      - GEOLOCATION_LNG=8.6784
      - ACCEPT_LANGUAGE=de-DE,de;q=0.9,en;q=0.8
      - LANG=de_DE.UTF-8
      - LANGUAGE=de_DE:de:en
      - LC_ALL=de_DE.UTF-8
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
