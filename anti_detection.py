import random
import asyncio
import logging
from typing import Dict, Any
from playwright.async_api import Page, BrowserContext
from playwright_stealth import stealth_async
from fake_useragent import UserAgent
from config import settings

logger = logging.getLogger(__name__)


class AntiDetectionManager:
    """Handles anti-bot detection measures for browser automation."""
    
    def __init__(self):
        self.ua = UserAgent()
        
    async def apply_stealth_mode(self, context: BrowserContext) -> None:
        """Apply stealth patches to browser context."""
        await stealth_async(context)
        
    def get_random_user_agent(self) -> str:
        """Get a random user agent string."""
        return random.choice(settings.user_agents)
        
    def get_browser_args(self) -> list:
        """Get browser launch arguments for anti-detection (German environment)."""
        return [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-field-trial-config',
            '--disable-ipc-flooding-protection',
            '--no-first-run',
            '--no-default-browser-check',
            '--no-pings',
            '--password-store=basic',
            '--use-mock-keychain',
            # German-specific browser settings
            f'--lang={settings.locale}',  # Set browser language to German
            '--accept-lang=de-DE,de,en',  # Accept German languages
            '--disable-translate',  # Disable translation prompts
            '--disable-sync',  # Disable Chrome sync
            '--disable-default-apps',  # Disable default apps
            '--disable-extensions-http-throttling',
        ]
        
    def get_context_options(self) -> Dict[str, Any]:
        """Get browser context options for anti-detection (German locale - Giessen)."""
        return {
            'user_agent': self.get_random_user_agent(),
            'viewport': {'width': 1920, 'height': 1080},
            'locale': settings.locale,  # German locale from config
            'timezone_id': settings.timezone,  # German timezone from config
            'permissions': ['geolocation'],
            'geolocation': {
                'latitude': settings.geolocation_lat,
                'longitude': settings.geolocation_lng
            },  # Giessen, Germany coordinates
            'extra_http_headers': {
                'Accept-Language': settings.accept_language,  # German language preference
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'DNT': '1',  # Do Not Track header common in German browsers
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Cache-Control': 'max-age=0',
            }
        }
        
    async def random_delay(self, min_delay: float = None, max_delay: float = None) -> None:
        """Add random delay to mimic human behavior."""
        min_d = min_delay or settings.min_delay
        max_d = max_delay or settings.max_delay
        delay = random.uniform(min_d, max_d)
        await asyncio.sleep(delay)
        
    async def human_like_typing(self, page: Page, selector: str, text: str) -> None:
        """Type text with human-like delays."""
        await page.click(selector)
        await self.random_delay(0.5, 1.0)
        
        for char in text:
            await page.keyboard.type(char)
            await asyncio.sleep(random.uniform(0.05, settings.typing_delay))
            
    async def random_mouse_movement(self, page: Page) -> None:
        """Perform random mouse movements to mimic human behavior."""
        for _ in range(random.randint(1, 3)):
            x = random.randint(100, 1800)
            y = random.randint(100, 1000)
            await page.mouse.move(x, y)
            await self.random_delay(0.1, 0.5)
            
    async def scroll_randomly(self, page: Page) -> None:
        """Perform random scrolling to mimic human behavior."""
        scroll_amount = random.randint(100, 500)
        await page.mouse.wheel(0, scroll_amount)
        await self.random_delay(0.5, 1.5)

    async def setup_german_environment(self, page: Page) -> None:
        """Set up German-specific browser environment settings."""
        try:
            # Set navigator language properties to German
            await page.add_init_script("""
                Object.defineProperty(navigator, 'language', {
                    get: () => 'de-DE'
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['de-DE', 'de', 'en']
                });

                // Override timezone
                const originalDateTimeFormat = Intl.DateTimeFormat;
                Intl.DateTimeFormat = function(...args) {
                    if (args.length === 0 || (args.length === 1 && typeof args[0] === 'object' && !args[0].timeZone)) {
                        args = ['de-DE', { timeZone: 'Europe/Berlin', ...args[0] }];
                    }
                    return new originalDateTimeFormat(...args);
                };

                // Set German number format
                const originalNumberFormat = Intl.NumberFormat;
                Intl.NumberFormat = function(...args) {
                    if (args.length === 0) {
                        args = ['de-DE'];
                    }
                    return new originalNumberFormat(...args);
                };

                // Override geolocation to Giessen coordinates
                if (navigator.geolocation) {
                    const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
                    navigator.geolocation.getCurrentPosition = function(success, error, options) {
                        const position = {
                            coords: {
                                latitude: 50.5840,
                                longitude: 8.6784,
                                accuracy: 20,
                                altitude: null,
                                altitudeAccuracy: null,
                                heading: null,
                                speed: null
                            },
                            timestamp: Date.now()
                        };
                        success(position);
                    };
                }
            """)

            # Set additional German locale settings
            await page.evaluate("""
                // Set German date/time formatting
                Date.prototype.toLocaleString = function() {
                    return new Intl.DateTimeFormat('de-DE', {
                        timeZone: 'Europe/Berlin',
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    }).format(this);
                };
            """)

        except Exception as e:
            logger.debug(f"Could not set up German environment: {e}")
