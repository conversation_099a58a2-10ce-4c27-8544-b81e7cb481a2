#!/bin/bash

echo "🔍 Debugging FastAPI Issues"
echo "==========================="

# Check if container is running
if ! docker-compose ps | grep -q "perplexity-agent"; then
    echo "❌ Container is not running. Starting it first..."
    docker-compose up -d
    sleep 10
fi

echo "Container status:"
docker-compose ps

echo ""
echo "Supervisor status:"
docker-compose exec perplexity-agent supervisorctl status

echo ""
echo "FastAPI error logs:"
docker-compose exec perplexity-agent supervisorctl tail fastapi stderr

echo ""
echo "FastAPI stdout logs:"
docker-compose exec perplexity-agent supervisorctl tail fastapi stdout

echo ""
echo "Testing Python imports manually:"
docker-compose exec perplexity-agent python3 -c "
try:
    import sys
    print(f'Python version: {sys.version}')
    print(f'Python path: {sys.executable}')
    
    print('Testing imports...')
    import fastapi
    print('✅ FastAPI imported successfully')
    
    import uvicorn
    print('✅ Uvicorn imported successfully')
    
    import playwright
    print('✅ Playwright imported successfully')
    
    import redis
    print('✅ Redis imported successfully')
    
    print('Testing main.py import...')
    import main
    print('✅ main.py imported successfully')
    
except Exception as e:
    print(f'❌ Import error: {e}')
    import traceback
    traceback.print_exc()
"

echo ""
echo "Testing manual FastAPI start:"
docker-compose exec perplexity-agent bash -c "cd /app && python3 -c 'import main; print(\"FastAPI app object:\", main.app)'"
