#!/bin/bash

echo "🔄 Rebuilding and Testing German Perplexity Agent"
echo "================================================"

# Stop existing containers
echo "Stopping existing containers..."
docker-compose down -v

# Rebuild with no cache
echo "Rebuilding container..."
docker-compose build --no-cache

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    
    # Start services
    echo "Starting services..."
    docker-compose up -d
    
    # Wait for services to start
    echo "Waiting for services to start..."
    sleep 30
    
    # Check container status
    echo "Container status:"
    docker-compose ps
    
    # Check logs for any errors
    echo ""
    echo "Recent logs:"
    docker-compose logs --tail=20
    
    # Test health endpoint
    echo ""
    echo "Testing health endpoint..."
    for i in {1..10}; do
        if curl -s http://localhost:8000/health >/dev/null 2>&1; then
            echo "✅ API is responding!"
            curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null
            break
        else
            echo "Waiting for API... ($i/10)"
            sleep 5
        fi
    done
    
    echo ""
    echo "🎉 Setup complete!"
    echo "Access points:"
    echo "  🌐 API: http://localhost:8000"
    echo "  🖥️  VNC: localhost:5900"
    echo "  📚 Docs: http://localhost:8000/docs"
    
else
    echo "❌ Build failed!"
    exit 1
fi
