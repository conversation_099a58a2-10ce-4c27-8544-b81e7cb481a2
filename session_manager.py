import json
import uuid
import asyncio
from typing import Dict, Any, Optional
import redis.asyncio as redis
from config import settings
import logging

logger = logging.getLogger(__name__)


class SessionManager:
    """Manages Redis-based session storage for concurrent requests."""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        
    async def initialize(self) -> None:
        """Initialize Redis connection."""
        try:
            self.redis_client = redis.Redis.from_url(settings.redis_url)
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis connection established successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
            
    async def create_session(self, query_data: Dict[str, Any]) -> str:
        """Create a new session and return session ID."""
        session_id = str(uuid.uuid4())
        session_data = {
            **query_data,
            "status": "created",
            "created_at": asyncio.get_event_loop().time()
        }
        
        try:
            await self.redis_client.setex(
                f"session:{session_id}",
                settings.redis_session_ttl,
                json.dumps(session_data)
            )
            logger.info(f"Session created: {session_id}")
            return session_id
        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            raise
            
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve session data by session ID."""
        try:
            data = await self.redis_client.get(f"session:{session_id}")
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            return None
            
    async def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Update session data."""
        try:
            existing_data = await self.get_session(session_id)
            if not existing_data:
                logger.warning(f"Session {session_id} not found for update")
                return False
                
            updated_data = {**existing_data, **data}
            await self.redis_client.setex(
                f"session:{session_id}",
                settings.redis_session_ttl,
                json.dumps(updated_data)
            )
            logger.debug(f"Session updated: {session_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to update session {session_id}: {e}")
            return False
            
    async def delete_session(self, session_id: str) -> bool:
        """Delete session data."""
        try:
            result = await self.redis_client.delete(f"session:{session_id}")
            if result:
                logger.info(f"Session deleted: {session_id}")
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            return False
            
    async def get_active_sessions(self) -> list:
        """Get list of all active session IDs."""
        try:
            keys = await self.redis_client.keys("session:*")
            return [key.decode().split(":", 1)[1] for key in keys]
        except Exception as e:
            logger.error(f"Failed to get active sessions: {e}")
            return []
            
    async def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions and return count of cleaned sessions."""
        try:
            active_sessions = await self.get_active_sessions()
            cleaned_count = 0
            
            for session_id in active_sessions:
                session_data = await self.get_session(session_id)
                if not session_data:
                    cleaned_count += 1
                    continue
                    
                # Check if session is older than TTL
                created_at = session_data.get("created_at", 0)
                current_time = asyncio.get_event_loop().time()
                if current_time - created_at > settings.redis_session_ttl:
                    await self.delete_session(session_id)
                    cleaned_count += 1
                    
            logger.info(f"Cleaned up {cleaned_count} expired sessions")
            return cleaned_count
        except Exception as e:
            logger.error(f"Failed to cleanup expired sessions: {e}")
            return 0
            
    async def close(self) -> None:
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Redis connection closed")


# Global session manager instance
session_manager = SessionManager()
