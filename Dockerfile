FROM mcr.microsoft.com/playwright/python:v1.40.0-jammy

# Set environment variables to avoid interactive prompts
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Europe/Berlin

# Install VNC and desktop environment
RUN apt-get update && apt-get install -y \
    tzdata \
    locales \
    xvfb \
    x11vnc \
    fluxbox \
    wget \
    wmctrl \
    redis-server \
    supervisor \
    curl \
    git \
    python3-numpy \
    && rm -rf /var/lib/apt/lists/*

# Install noVNC and websockify
RUN git clone https://github.com/novnc/noVNC.git /opt/noVNC \
    && git clone https://github.com/novnc/websockify /opt/websockify \
    && ln -s /opt/noVNC/vnc.html /opt/noVNC/index.html

# Configure timezone and locale for German localization
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN sed -i '/en_US.UTF-8/s/^# //g' /etc/locale.gen && \
    sed -i '/de_DE.UTF-8/s/^# //g' /etc/locale.gen && \
    locale-gen

# Set German locale environment variables
ENV LANG=de_DE.UTF-8
ENV LANGUAGE=de_DE:de:en
ENV LC_ALL=de_DE.UTF-8

# Set up environment
ENV DISPLAY=:1
ENV PYTHONPATH=/app
ENV REDIS_URL=redis://localhost:6379

# German localization environment variables
ENV LOCALE=de-DE
ENV TIMEZONE=Europe/Berlin
ENV GEOLOCATION_LAT=50.5840
ENV GEOLOCATION_LNG=8.6784
ENV ACCEPT_LANGUAGE=de-DE,de;q=0.9,en;q=0.8

# Create app directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers with stealth patches
RUN playwright install chromium
RUN playwright install-deps

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/chrome_profile && chmod 755 /app/chrome_profile
RUN mkdir -p /app/logs && chmod 755 /app/logs
RUN mkdir -p /var/log/supervisor

# Find Python path and create a symlink for consistency
RUN which python3 && ln -sf $(which python3) /usr/local/bin/python3

# Create supervisor configuration
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose ports
EXPOSE 5900 8000 6379

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
