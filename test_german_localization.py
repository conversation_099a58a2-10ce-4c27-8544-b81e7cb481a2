#!/usr/bin/env python3
"""
Test script specifically for German localization of the Perplexity Agent.
This script tests that the agent properly simulates a German user from Giessen.
"""

import requests
import json
import time
import sys


def test_german_query(base_url):
    """Test a query in German to verify localization."""
    print("Testing German query...")
    try:
        query_data = {
            "query": "Was ist die Hauptstadt von Deutschland?",  # "What is the capital of Germany?"
            "timeout": 30
        }
        
        print(f"Sending German query: {query_data['query']}")
        response = requests.post(
            f"{base_url}/query", 
            json=query_data, 
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ German query successful!")
            print(f"   Session ID: {data['session_id']}")
            print(f"   Execution time: {data['execution_time']:.2f}s")
            print(f"   Result length: {len(data['result'])} characters")
            print(f"   Sources found: {len(data['sources'])}")
            
            # Check if response contains German text
            result_text = data['result'].lower()
            german_indicators = ['berlin', 'deutschland', 'hauptstadt', 'die', 'der', 'das', 'ist', 'eine']
            german_found = any(indicator in result_text for indicator in german_indicators)
            
            if german_found:
                print(f"   ✅ Response appears to be in German!")
            else:
                print(f"   ⚠️  Response may not be in German")
                
            print(f"   Result preview: {data['result'][:300]}...")
            return True
        else:
            print(f"❌ German query failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ German query error: {e}")
        return False


def test_german_current_events(base_url):
    """Test a German query about current events."""
    print("\nTesting German current events query...")
    try:
        query_data = {
            "query": "Aktuelle Nachrichten aus Deutschland heute",  # "Current news from Germany today"
            "use_deep_search": True,
            "timeout": 45
        }
        
        print(f"Sending German news query: {query_data['query']}")
        response = requests.post(
            f"{base_url}/query", 
            json=query_data, 
            timeout=90
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ German news query successful!")
            print(f"   Session ID: {data['session_id']}")
            print(f"   Execution time: {data['execution_time']:.2f}s")
            print(f"   Result length: {len(data['result'])} characters")
            print(f"   Sources found: {len(data['sources'])}")
            
            # Check for German news sources
            sources = data.get('sources', [])
            german_domains = ['.de', 'spiegel', 'zeit', 'faz', 'sueddeutsche', 'bild', 'welt']
            german_sources = [s for s in sources if any(domain in s.get('url', '').lower() for domain in german_domains)]
            
            if german_sources:
                print(f"   ✅ Found {len(german_sources)} German news sources!")
                for source in german_sources[:3]:
                    print(f"     - {source.get('title', 'Unknown')}: {source.get('url', '')}")
            else:
                print(f"   ⚠️  No obvious German sources found")
                
            return True
        else:
            print(f"❌ German news query failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ German news query error: {e}")
        return False


def test_german_local_query(base_url):
    """Test a query about Giessen, Germany (our simulated location)."""
    print("\nTesting Giessen local query...")
    try:
        query_data = {
            "query": "Wetter in Gießen heute",  # "Weather in Giessen today"
            "timeout": 30
        }
        
        print(f"Sending Giessen query: {query_data['query']}")
        response = requests.post(
            f"{base_url}/query", 
            json=query_data, 
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Giessen query successful!")
            print(f"   Session ID: {data['session_id']}")
            print(f"   Execution time: {data['execution_time']:.2f}s")
            print(f"   Result length: {len(data['result'])} characters")
            
            # Check if response mentions Giessen
            result_text = data['result'].lower()
            if 'gießen' in result_text or 'giessen' in result_text:
                print(f"   ✅ Response mentions Giessen!")
            else:
                print(f"   ⚠️  Response may not be location-specific")
                
            print(f"   Result preview: {data['result'][:200]}...")
            return True
        else:
            print(f"❌ Giessen query failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Giessen query error: {e}")
        return False


def test_mixed_language_query(base_url):
    """Test a mixed German-English query."""
    print("\nTesting mixed language query...")
    try:
        query_data = {
            "query": "Unterschied zwischen artificial intelligence und künstlicher Intelligenz",
            "timeout": 30
        }
        
        print(f"Sending mixed query: {query_data['query']}")
        response = requests.post(
            f"{base_url}/query", 
            json=query_data, 
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Mixed language query successful!")
            print(f"   Session ID: {data['session_id']}")
            print(f"   Execution time: {data['execution_time']:.2f}s")
            print(f"   Result length: {len(data['result'])} characters")
            print(f"   Result preview: {data['result'][:200]}...")
            return True
        else:
            print(f"❌ Mixed language query failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Mixed language query error: {e}")
        return False


def main():
    """Run German localization tests."""
    base_url = "http://localhost:8000"
    
    print("🇩🇪 Starting German Localization Tests for Perplexity Agent")
    print(f"Testing against: {base_url}")
    print("Location: Giessen, Germany (50.5840, 8.6784)")
    print("Locale: de-DE, Timezone: Europe/Berlin")
    print("=" * 60)
    
    # Wait for service to be ready
    print("Waiting for service to be ready...")
    for i in range(30):
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Service ready! Status: {health_data.get('status')}")
                break
        except:
            pass
        time.sleep(2)
        print(f"  Waiting... ({i+1}/30)")
    
    # Run German-specific tests
    tests = [
        test_german_query,
        test_german_current_events,
        test_german_local_query,
        test_mixed_language_query
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func(base_url):
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} crashed: {e}")
        
        time.sleep(3)  # Pause between tests
    
    print("\n" + "=" * 60)
    print(f"🏁 German Localization Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All German localization tests passed!")
        print("The agent successfully simulates a German user from Giessen.")
    elif passed > 0:
        print("⚠️  Some tests passed. The German localization is partially working.")
    else:
        print("❌ German localization tests failed. Check configuration.")
    
    print("\n📋 Expected behaviors:")
    print("- Responses should be in German when queries are in German")
    print("- German news sources should appear for German news queries")
    print("- Location-based queries should reference Giessen/Germany")
    print("- Browser should appear to be from Germany (check VNC)")
    
    sys.exit(0 if passed == total else 1)


if __name__ == "__main__":
    main()
