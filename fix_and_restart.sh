#!/bin/bash

echo "🔧 Fixing Browser Issues and Restarting"
echo "======================================="

# Stop current containers
echo "Stopping containers..."
docker-compose down

# Rebuild with fixes
echo "Rebuilding with browser fixes..."
docker-compose build --no-cache

# Start services
echo "Starting services..."
docker-compose up -d

# Wait for startup
echo "Waiting for services to start..."
sleep 20

# Check status
echo "Checking service status..."
docker-compose ps

echo ""
echo "Checking supervisor status..."
docker-compose exec perplexity-agent supervisorctl status

echo ""
echo "Testing API access..."
for i in {1..10}; do
    echo "Attempt $i/10..."
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ API is accessible!"
        echo "Health check response:"
        curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null
        break
    else
        echo "API not ready yet, waiting..."
        sleep 5
    fi
done

echo ""
echo "🎉 Access points:"
echo "  🌐 API: http://localhost:8000"
echo "  📚 Docs: http://localhost:8000/docs"
echo "  🖥️  VNC: localhost:5900"

echo ""
echo "Test German query:"
echo 'curl -X POST "http://localhost:8000/query" -H "Content-Type: application/json" -d '"'"'{"query": "Was ist die Hauptstadt von Deutschland?"}'"'"''
