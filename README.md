# Perplexity AI Agent

An AI agent for automating Perplexity queries through browser automation with visual debugging capabilities and anti-bot detection measures.

## Features

- **Browser Automation**: Uses Playwright with stealth mode for reliable Perplexity.ai interaction
- **German Localization**: Simulates German user from Giessen with German language preferences
- **Visual Debugging**: VNC server for real-time browser monitoring
- **Concurrent Requests**: Single browser instance with multiple tabs for efficient resource usage
- **Anti-Bot Detection**: Advanced evasion techniques including stealth mode, randomized timing, and human-like behavior
- **Session Management**: Redis-based session storage with automatic cleanup
- **Profile Import**: Support for Chrome profile mounting to maintain login sessions
- **RESTful API**: FastAPI-based API for easy integration

## Quick Start

### Using Docker Compose

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd BellingByte-PerplexiGate
   ```

2. **Build and run**:
   ```bash
   # Build and start all services
   docker-compose up --build

   # Or run in background
   docker-compose up --build -d
   ```

3. **Access the services**:
   - **API**: http://localhost:8000
   - **VNC (Visual Debugging)**: localhost:5900 (no password required)
   - **API Documentation**: http://localhost:8000/docs

4. **Stop services**:
   ```bash
   docker-compose down
   ```

### Docker Compose Commands

```bash
# Build and start
docker-compose up --build

# Start in background
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild from scratch
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d

# Check service status
docker-compose ps
```

## API Usage

### Basic Query (English)

```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is the latest news about AI?"
  }'
```

### German Query (Localized Response)

```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Was sind die neuesten Nachrichten über KI?"
  }'
```

### Advanced Query with Deep Search

```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Klimawandel Auswirkungen 2024",
    "use_deep_search": true,
    "sources": ["scientific", "news"],
    "timeout": 60
  }'
```

### Health Check

```bash
curl http://localhost:8000/health
```

### Get Active Sessions

```bash
curl http://localhost:8000/sessions
```

## Visual Debugging

Connect to the VNC server to watch browser automation in real-time:

1. **Using VNC Viewer**:
   - Host: `localhost:5900`
   - Password: `secret`

2. **Using web-based VNC** (if available):
   - URL: `http://localhost:6080`

## Chrome Profile Import

To maintain login sessions and preferences:

1. **Export your Chrome profile**:
   - Copy your Chrome profile directory (usually in `~/.config/google-chrome/Default` on Linux)

2. **Mount the profile**:
   ```bash
   docker run -v /path/to/your/chrome/profile:/app/chrome_profile perplexity-agent
   ```

## German Localization

The agent is configured to simulate a German user located in Giessen, Germany:

### Location Settings
- **City**: Giessen, Germany
- **Coordinates**: 50.5840°N, 8.6784°E
- **Timezone**: Europe/Berlin
- **Locale**: de-DE

### Language Configuration
- **Browser Language**: German (de-DE)
- **Accept-Language**: de-DE,de;q=0.9,en;q=0.8
- **Number/Date Format**: German formatting
- **Geolocation**: Giessen coordinates

### Expected Behavior
- German queries receive German responses
- German news sources appear for news queries
- Location-based queries reference German locations
- Browser appears to originate from Germany

### Testing German Localization
```bash
# Run German-specific tests
python test_german_localization.py

# Or run tests inside container
docker-compose exec perplexity-agent python test_german_localization.py

# Test German query
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "Wetter in Gießen heute"}'
```

## Configuration

Key configuration options in `.env`:

- `LOCALE`: Browser locale (default: de-DE)
- `TIMEZONE`: Browser timezone (default: Europe/Berlin)
- `GEOLOCATION_LAT/LNG`: GPS coordinates (default: Giessen)
- `ACCEPT_LANGUAGE`: Language preferences (default: German priority)
- `MAX_CONCURRENT_REQUESTS`: Maximum simultaneous queries (default: 10)
- `BROWSER_HEADLESS`: Set to `true` to run without VNC (default: false)
- `VNC_PASSWORD`: Password for VNC access (default: secret)
- `REDIS_SESSION_TTL`: Session timeout in seconds (default: 3600)

## API Endpoints

- `POST /query` - Execute a Perplexity query
- `GET /health` - Health check and status
- `GET /sessions` - List active sessions
- `DELETE /sessions/{session_id}` - Delete specific session
- `POST /browser/restart` - Restart browser instance

## Architecture

- **FastAPI**: Async web framework for handling concurrent requests
- **Playwright**: Browser automation with stealth capabilities
- **Redis**: Session management and temporary storage
- **Docker**: Containerized deployment with VNC support

## Anti-Bot Detection Features

- Playwright stealth mode
- Randomized user agents and headers
- Human-like typing and mouse movements
- Random delays between actions
- Canvas fingerprint randomization
- Geolocation spoofing

## Troubleshooting

### Browser Issues
```bash
# Restart browser instance
curl -X POST http://localhost:8000/browser/restart
```

### Session Cleanup
Sessions are automatically cleaned up after 1 hour. Manual cleanup:
```bash
# Delete specific session
curl -X DELETE http://localhost:8000/sessions/{session_id}
```

### VNC Connection Issues
- Ensure port 5900 is not blocked by firewall
- Check VNC password is set correctly
- Verify container is running: `docker ps`

### Logs
```bash
# View container logs
docker logs perplexity-agent

# View application logs
docker exec perplexity-agent tail -f /app/logs/perplexity_agent.log
```

## Development

### Local Development Setup

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   playwright install chromium
   ```

2. **Start Redis**:
   ```bash
   redis-server
   ```

3. **Run application**:
   ```bash
   python main.py
   ```

### Testing

```bash
# Test basic functionality
python -m pytest tests/

# Test with specific query
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "test query"}'
```

## Security Considerations

- This is designed for personal use and testing
- Implement proper authentication for production use
- Monitor rate limits to avoid being blocked by Perplexity
- Use VPN or proxy rotation for high-volume usage

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
- Check the troubleshooting section
- Review container logs
- Open an issue on GitHub
