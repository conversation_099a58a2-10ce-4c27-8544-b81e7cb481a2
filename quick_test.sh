#!/bin/bash

echo "🇩🇪 Quick Test for German Perplexity Agent"
echo "=========================================="

# Check if services are running
echo "Checking service health..."
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ API is responding"
    curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null
else
    echo "❌ API is not responding. Make sure services are running:"
    echo "   docker-compose up -d"
    exit 1
fi

echo ""
echo "Testing German query..."
response=$(curl -s -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "Was ist die Hauptstadt von Deutschland?"}')

if [ $? -eq 0 ]; then
    echo "✅ German query successful!"
    echo "Response preview:"
    echo "$response" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(f'  Result: {data[\"result\"][:200]}...')
    print(f'  Sources: {len(data[\"sources\"])} found')
    print(f'  Execution time: {data[\"execution_time\"]:.2f}s')
except:
    print('  Could not parse response')
"
else
    echo "❌ German query failed"
fi

echo ""
echo "🔗 Access points:"
echo "  API: http://localhost:8000"
echo "  VNC: localhost:5900 (no password)"
echo "  Docs: http://localhost:8000/docs"
