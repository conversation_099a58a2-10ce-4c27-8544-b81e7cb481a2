#!/bin/bash

echo "🔍 Troubleshooting Container Access Issues"
echo "========================================="

# Check if containers are running
echo "1. Checking container status:"
docker-compose ps

echo ""
echo "2. Checking port bindings:"
docker-compose port perplexity-agent 8000 2>/dev/null || echo "Port 8000 not accessible"

echo ""
echo "3. Checking if port 8000 is in use:"
lsof -i :8000 2>/dev/null || echo "Port 8000 is not in use by any process"

echo ""
echo "4. Checking container logs:"
docker-compose logs --tail=20 perplexity-agent

echo ""
echo "5. Checking supervisor status inside container:"
docker-compose exec perplexity-agent supervisorctl status 2>/dev/null || echo "Cannot access container"

echo ""
echo "6. Testing internal container connectivity:"
docker-compose exec perplexity-agent curl -s http://localhost:8000/health 2>/dev/null || echo "Internal API not responding"

echo ""
echo "7. Checking FastAPI service logs:"
docker-compose exec perplexity-agent supervisorctl tail fastapi stderr 2>/dev/null || echo "Cannot get FastAPI logs"

echo ""
echo "8. Testing different access methods:"
echo "Testing localhost:8000..."
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health 2>/dev/null || echo "localhost:8000 - Connection failed"

echo "Testing 127.0.0.1:8000..."
curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:8000/health 2>/dev/null || echo "127.0.0.1:8000 - Connection failed"

echo "Testing 0.0.0.0:8000..."
curl -s -o /dev/null -w "%{http_code}" http://0.0.0.0:8000/health 2>/dev/null || echo "0.0.0.0:8000 - Connection failed"

echo ""
echo "9. Checking Docker network:"
docker network ls | grep perplexigate || echo "No custom network found"

echo ""
echo "🔧 Suggested fixes:"
echo "1. Restart containers: docker-compose restart"
echo "2. Rebuild containers: docker-compose down -v && docker-compose up --build"
echo "3. Check firewall settings"
echo "4. Try different URLs:"
echo "   - http://localhost:8000/docs"
echo "   - http://127.0.0.1:8000/docs"
echo "   - http://0.0.0.0:8000/docs"
