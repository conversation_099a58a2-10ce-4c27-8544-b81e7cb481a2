import asyncio
import logging
from typing import Dict, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright
from anti_detection import AntiDetectionManager
from config import settings

logger = logging.getLogger(__name__)


class BrowserManager:
    """Manages single browser instance with multiple tabs for concurrent requests."""
    
    def __init__(self):
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.active_tabs: Dict[str, Page] = {}
        self.anti_detection = AntiDetectionManager()
        self._lock = asyncio.Lock()
        self._initialized = False
        
    async def initialize(self) -> None:
        """Initialize browser instance with anti-detection measures."""
        async with self._lock:
            if self._initialized:
                return

            try:
                logger.info("Initializing browser manager...")

                # Start Playwright
                self.playwright = await async_playwright().start()

                # Use persistent context with user data directory
                context_options = self.anti_detection.get_context_options()
                context_options['headless'] = settings.browser_headless
                context_options['args'] = self.anti_detection.get_browser_args()

                # Add VNC-specific environment variables
                import os
                os.environ['DISPLAY'] = settings.display

                logger.info(f"Using display: {settings.display}")
                logger.info(f"Browser headless mode: {settings.browser_headless}")

                # Launch persistent context (includes browser and context)
                self.context = await self.playwright.chromium.launch_persistent_context(
                    user_data_dir=settings.chrome_profile_path,
                    **context_options
                )

                # For compatibility, set browser reference
                self.browser = self.context.browser

                # Apply stealth mode
                await self.anti_detection.apply_stealth_mode(self.context)

                # Set default timeouts
                self.context.set_default_timeout(settings.browser_timeout)
                self.context.set_default_navigation_timeout(settings.browser_timeout)

                self._initialized = True
                logger.info("Browser manager initialized successfully")

            except Exception as e:
                logger.error(f"Failed to initialize browser manager: {e}")
                await self.cleanup()
                raise
                
    async def create_tab(self, session_id: str) -> Page:
        """Create a new tab for the given session."""
        if not self._initialized:
            await self.initialize()
            
        async with self._lock:
            try:
                if session_id in self.active_tabs:
                    logger.warning(f"Tab already exists for session {session_id}")
                    return self.active_tabs[session_id]
                    
                # Create new page
                page = await self.context.new_page()

                # Set page-specific configurations
                await page.set_viewport_size({"width": 1920, "height": 1080})

                # Apply German environment settings
                await self.anti_detection.setup_german_environment(page)

                # Store tab reference
                self.active_tabs[session_id] = page
                
                logger.info(f"Created new tab for session: {session_id}")
                return page
                
            except Exception as e:
                logger.error(f"Failed to create tab for session {session_id}: {e}")
                raise
                
    async def get_tab(self, session_id: str) -> Optional[Page]:
        """Get existing tab for session."""
        return self.active_tabs.get(session_id)
        
    async def close_tab(self, session_id: str) -> bool:
        """Close tab for the given session."""
        async with self._lock:
            try:
                if session_id not in self.active_tabs:
                    logger.warning(f"No tab found for session {session_id}")
                    return False
                    
                page = self.active_tabs[session_id]
                await page.close()
                del self.active_tabs[session_id]
                
                logger.info(f"Closed tab for session: {session_id}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to close tab for session {session_id}: {e}")
                return False
                
    async def get_active_tab_count(self) -> int:
        """Get count of active tabs."""
        return len(self.active_tabs)
        
    async def get_active_sessions(self) -> list:
        """Get list of active session IDs."""
        return list(self.active_tabs.keys())
        
    async def cleanup_orphaned_tabs(self) -> int:
        """Clean up tabs that may have been orphaned."""
        cleaned_count = 0
        sessions_to_remove = []
        
        for session_id, page in self.active_tabs.items():
            try:
                # Check if page is still valid
                await page.title()
            except Exception:
                sessions_to_remove.append(session_id)
                
        for session_id in sessions_to_remove:
            await self.close_tab(session_id)
            cleaned_count += 1
            
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} orphaned tabs")
            
        return cleaned_count
        
    async def restart_browser(self) -> None:
        """Restart browser instance (useful for recovery)."""
        logger.info("Restarting browser...")
        await self.cleanup()
        await self.initialize()
        
    async def cleanup(self) -> None:
        """Clean up all browser resources."""
        async with self._lock:
            try:
                # Close all active tabs
                for session_id in list(self.active_tabs.keys()):
                    await self.close_tab(session_id)
                    
                # Close persistent context (this also closes the browser)
                if self.context:
                    await self.context.close()
                    self.context = None
                    self.browser = None
                    
                # Stop playwright
                if self.playwright:
                    await self.playwright.stop()
                    self.playwright = None
                    
                self._initialized = False
                logger.info("Browser manager cleaned up successfully")
                
            except Exception as e:
                logger.error(f"Error during browser cleanup: {e}")


# Global browser manager instance
browser_manager = BrowserManager()
