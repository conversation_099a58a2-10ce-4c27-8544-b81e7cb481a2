import asyncio
import logging
import os
from contextlib import asynccontextmanager
from typing import List, Optional

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from config import settings
from session_manager import session_manager
from browser_manager import browser_manager
from perplexity_automation import perplexity_automation

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(settings.log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# Pydantic models
class QueryRequest(BaseModel):
    query: str = Field(..., description="The query to send to Perplexity", min_length=1, max_length=1000)
    use_deep_search: bool = Field(False, description="Enable deep search/Pro search if available")
    sources: List[str] = Field(default_factory=list, description="Specific sources to use")
    timeout: int = Field(30, description="Timeout in seconds", ge=10, le=120)


class QueryResponse(BaseModel):
    result: str = Field(..., description="The answer from Perplexity")
    sources: List[dict] = Field(..., description="List of sources used")
    execution_time: float = Field(..., description="Time taken to execute query")
    session_id: str = Field(..., description="Session ID for this query")


class HealthResponse(BaseModel):
    status: str
    browser_initialized: bool
    redis_connected: bool
    active_sessions: int
    active_tabs: int


class SessionInfo(BaseModel):
    session_id: str
    status: str
    query: Optional[str] = None
    execution_time: Optional[float] = None


# Application lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application startup and shutdown."""
    # Startup
    logger.info("Starting Perplexity Agent...")
    
    try:
        # Initialize session manager
        await session_manager.initialize()
        
        # Initialize browser manager
        await browser_manager.initialize()
        
        logger.info("Perplexity Agent started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Perplexity Agent...")
    
    try:
        # Cleanup browser resources
        await browser_manager.cleanup()
        
        # Close session manager
        await session_manager.close()
        
        logger.info("Perplexity Agent shut down successfully")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI app
app = FastAPI(
    title="Perplexity AI Agent",
    description="AI agent for automating Perplexity queries through browser automation",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Background task for cleanup
async def cleanup_expired_sessions():
    """Background task to clean up expired sessions."""
    try:
        await session_manager.cleanup_expired_sessions()
        await browser_manager.cleanup_orphaned_tabs()
    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")


# API endpoints
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    try:
        # Check Redis connection
        redis_connected = False
        try:
            await session_manager.redis_client.ping()
            redis_connected = True
        except:
            pass
        
        # Check browser status
        browser_initialized = browser_manager._initialized
        
        # Get active counts
        active_sessions = len(await session_manager.get_active_sessions())
        active_tabs = await browser_manager.get_active_tab_count()
        
        return HealthResponse(
            status="healthy" if redis_connected and browser_initialized else "unhealthy",
            browser_initialized=browser_initialized,
            redis_connected=redis_connected,
            active_sessions=active_sessions,
            active_tabs=active_tabs
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@app.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest, background_tasks: BackgroundTasks):
    """Process a Perplexity query."""
    try:
        # Check if we're at max concurrent requests
        active_sessions = len(await session_manager.get_active_sessions())
        if active_sessions >= settings.max_concurrent_requests:
            raise HTTPException(
                status_code=429, 
                detail=f"Maximum concurrent requests ({settings.max_concurrent_requests}) reached"
            )
        
        # Create session
        session_data = {
            "query": request.query,
            "use_deep_search": request.use_deep_search,
            "sources": request.sources,
            "timeout": request.timeout
        }
        session_id = await session_manager.create_session(session_data)
        
        # Add cleanup task
        background_tasks.add_task(cleanup_expired_sessions)
        
        # Execute query
        result = await perplexity_automation.execute_query(
            session_id=session_id,
            query=request.query,
            use_deep_search=request.use_deep_search,
            sources=request.sources,
            timeout=request.timeout
        )
        
        return QueryResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Query processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/sessions", response_model=List[SessionInfo])
async def get_active_sessions():
    """Get list of active sessions."""
    try:
        session_ids = await session_manager.get_active_sessions()
        sessions = []
        
        for session_id in session_ids:
            session_data = await session_manager.get_session(session_id)
            if session_data:
                sessions.append(SessionInfo(
                    session_id=session_id,
                    status=session_data.get("status", "unknown"),
                    query=session_data.get("query"),
                    execution_time=session_data.get("execution_time")
                ))
                
        return sessions
        
    except Exception as e:
        logger.error(f"Failed to get sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get sessions")


@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a specific session."""
    try:
        # Close browser tab if exists
        await browser_manager.close_tab(session_id)
        
        # Delete session data
        deleted = await session_manager.delete_session(session_id)
        
        if not deleted:
            raise HTTPException(status_code=404, detail="Session not found")
            
        return {"message": f"Session {session_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete session")


@app.post("/browser/restart")
async def restart_browser():
    """Restart the browser instance."""
    try:
        await browser_manager.restart_browser()
        return {"message": "Browser restarted successfully"}
        
    except Exception as e:
        logger.error(f"Failed to restart browser: {e}")
        raise HTTPException(status_code=500, detail="Failed to restart browser")


if __name__ == "__main__":
    # Create logs directory
    os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)
    
    # Run the application
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=False,
        log_level=settings.log_level.lower()
    )
