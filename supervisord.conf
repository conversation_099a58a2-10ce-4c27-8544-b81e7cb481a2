[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
user=root

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:xvfb]
command=/usr/bin/Xvfb :1 -screen 0 1920x1080x16
autostart=true
autorestart=true
priority=100
stdout_logfile=/var/log/supervisor/xvfb.log
stderr_logfile=/var/log/supervisor/xvfb.log

[program:fluxbox]
command=/usr/bin/fluxbox -display :1
autostart=true
autorestart=true
priority=200
stdout_logfile=/var/log/supervisor/fluxbox.log
stderr_logfile=/var/log/supervisor/fluxbox.log
environment=DISPLAY=":1"

[program:x11vnc]
command=/usr/bin/x11vnc -display :1 -forever -create -shared -rfbport 5900 -nopw
autostart=true
autorestart=true
priority=300
stdout_logfile=/var/log/supervisor/x11vnc.log
stderr_logfile=/var/log/supervisor/x11vnc.log
environment=DISPLAY=":1"

[program:redis]
command=/usr/bin/redis-server --bind 127.0.0.1 --port 6379 --daemonize no
autostart=true
autorestart=true
priority=400
stdout_logfile=/var/log/supervisor/redis.log
stderr_logfile=/var/log/supervisor/redis.log

[program:novnc]
command=/usr/bin/python3 /opt/websockify/websockify.py --web /opt/noVNC 6080 localhost:5900
autostart=true
autorestart=true
priority=450
stdout_logfile=/var/log/supervisor/novnc.log
stderr_logfile=/var/log/supervisor/novnc.log

[program:fastapi]
command=/usr/local/bin/python3 -m uvicorn main:app --host 0.0.0.0 --port 8000
directory=/app
autostart=true
autorestart=true
priority=500
stdout_logfile=/var/log/supervisor/fastapi.log
stderr_logfile=/var/log/supervisor/fastapi.log
environment=PYTHONPATH="/app",DISPLAY=":1",REDIS_URL="redis://localhost:6379",LOCALE="de-DE",TIMEZONE="Europe/Berlin",GEOLOCATION_LAT="50.5840",GEOLOCATION_LNG="8.6784",ACCEPT_LANGUAGE="de-DE,de;q=0.9,en;q=0.8"
