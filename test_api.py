#!/usr/bin/env python3
"""
Simple test script for the Perplexity Agent API.
Run this after starting the Docker container to verify functionality.
"""

import requests
import json
import time
import sys


def test_health_check(base_url):
    """Test the health check endpoint."""
    print("Testing health check...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            print(f"   Browser initialized: {data['browser_initialized']}")
            print(f"   Redis connected: {data['redis_connected']}")
            print(f"   Active sessions: {data['active_sessions']}")
            print(f"   Active tabs: {data['active_tabs']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False


def test_simple_query(base_url):
    """Test a simple query."""
    print("\nTesting simple query...")
    try:
        query_data = {
            "query": "What is the capital of France?",
            "timeout": 30
        }
        
        print(f"Sending query: {query_data['query']}")
        response = requests.post(
            f"{base_url}/query", 
            json=query_data, 
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Query successful!")
            print(f"   Session ID: {data['session_id']}")
            print(f"   Execution time: {data['execution_time']:.2f}s")
            print(f"   Result length: {len(data['result'])} characters")
            print(f"   Sources found: {len(data['sources'])}")
            print(f"   Result preview: {data['result'][:200]}...")
            return True
        else:
            print(f"❌ Query failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Query error: {e}")
        return False


def test_deep_search_query(base_url):
    """Test a query with deep search enabled."""
    print("\nTesting deep search query...")
    try:
        query_data = {
            "query": "Latest developments in artificial intelligence 2024",
            "use_deep_search": True,
            "timeout": 45
        }
        
        print(f"Sending deep search query: {query_data['query']}")
        response = requests.post(
            f"{base_url}/query", 
            json=query_data, 
            timeout=90
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Deep search query successful!")
            print(f"   Session ID: {data['session_id']}")
            print(f"   Execution time: {data['execution_time']:.2f}s")
            print(f"   Result length: {len(data['result'])} characters")
            print(f"   Sources found: {len(data['sources'])}")
            return True
        else:
            print(f"❌ Deep search query failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Deep search query error: {e}")
        return False


def test_sessions_endpoint(base_url):
    """Test the sessions endpoint."""
    print("\nTesting sessions endpoint...")
    try:
        response = requests.get(f"{base_url}/sessions", timeout=10)
        if response.status_code == 200:
            sessions = response.json()
            print(f"✅ Sessions endpoint working!")
            print(f"   Active sessions: {len(sessions)}")
            for session in sessions:
                print(f"   - {session['session_id']}: {session['status']}")
            return True
        else:
            print(f"❌ Sessions endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Sessions endpoint error: {e}")
        return False


def test_concurrent_queries(base_url):
    """Test concurrent queries (simplified)."""
    print("\nTesting concurrent queries...")
    try:
        import threading
        import queue
        
        results = queue.Queue()
        
        def send_query(query_text, result_queue):
            try:
                query_data = {"query": query_text, "timeout": 30}
                response = requests.post(f"{base_url}/query", json=query_data, timeout=60)
                result_queue.put(("success", response.status_code, query_text))
            except Exception as e:
                result_queue.put(("error", str(e), query_text))
        
        # Send 3 concurrent queries
        queries = [
            "What is Python?",
            "What is JavaScript?", 
            "What is Docker?"
        ]
        
        threads = []
        for query in queries:
            thread = threading.Thread(target=send_query, args=(query, results))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Check results
        success_count = 0
        while not results.empty():
            status, code, query = results.get()
            if status == "success" and code == 200:
                success_count += 1
                print(f"   ✅ Query '{query[:30]}...' succeeded")
            else:
                print(f"   ❌ Query '{query[:30]}...' failed: {code}")
        
        print(f"✅ Concurrent test completed: {success_count}/{len(queries)} succeeded")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Concurrent test error: {e}")
        return False


def main():
    """Run all tests."""
    base_url = "http://localhost:8000"
    
    print("🚀 Starting Perplexity Agent API Tests")
    print(f"Testing against: {base_url}")
    print("=" * 50)
    
    # Wait for service to be ready
    print("Waiting for service to be ready...")
    for i in range(30):
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                break
        except:
            pass
        time.sleep(2)
        print(f"  Waiting... ({i+1}/30)")
    
    # Run tests
    tests = [
        test_health_check,
        test_simple_query,
        test_deep_search_query,
        test_sessions_endpoint,
        test_concurrent_queries
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func(base_url):
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} crashed: {e}")
        
        time.sleep(2)  # Brief pause between tests
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Perplexity Agent is working correctly.")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Check the logs and configuration.")
        sys.exit(1)


if __name__ == "__main__":
    main()
