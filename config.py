import os
from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Redis Configuration
    redis_url: str = "redis://localhost:6379"
    redis_session_ttl: int = 3600  # 1 hour
    
    # Browser Configuration
    browser_headless: bool = False  # Keep False for VNC debugging
    browser_timeout: int = 30000
    page_timeout: int = 60000
    
    # VNC Configuration
    vnc_password: str = "secret"
    display: str = ":1"
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    max_concurrent_requests: int = 10
    
    # Chrome Profile
    chrome_profile_path: str = "/app/chrome_profile"
    
    # Anti-Detection - German localized user agents
    user_agents: List[str] = [
        # Windows Chrome from Germany
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        # macOS Chrome from Germany
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        # Linux Chrome from Germany (common in German tech environments)
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ]
    
    # Perplexity Configuration
    perplexity_url: str = "https://perplexity.ai"
    query_selector: str = 'textarea[placeholder*="Ask anything"], textarea[placeholder*="Frag mich alles"], textarea[data-testid="search-input"], textarea'
    submit_selector: str = 'button[type="submit"], button[aria-label*="Submit"], button[aria-label*="Send"], [data-testid="submit-button"]'
    answer_selector: str = '[data-testid="answer-container"], [class*="answer"], [class*="response"], .prose'
    pro_search_toggle: str = '[data-testid="pro-search-toggle"], [aria-label*="Pro"], [class*="pro-toggle"]'
    
    # German Localization Configuration
    locale: str = "de-DE"
    timezone: str = "Europe/Berlin"
    geolocation_lat: float = 50.5840  # Giessen, Germany latitude
    geolocation_lng: float = 8.6784   # Giessen, Germany longitude
    accept_language: str = "de-DE,de;q=0.9,en;q=0.8"

    # Timing Configuration
    min_delay: float = 1.0
    max_delay: float = 3.0
    typing_delay: float = 0.1
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "/app/logs/perplexity_agent.log"
    
    class Config:
        env_file = ".env"


settings = Settings()
