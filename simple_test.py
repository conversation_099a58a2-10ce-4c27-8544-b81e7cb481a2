#!/usr/bin/env python3
"""
Simple test script to check what's failing in the FastAPI startup.
"""

import sys
import os
import traceback

print("🔍 Testing FastAPI startup components...")

try:
    print("1. Testing basic imports...")
    import asyncio
    import logging
    from contextlib import asynccontextmanager
    from typing import List, Optional
    print("✅ Basic imports successful")
    
    print("2. Testing FastAPI imports...")
    from fastapi import FastAPI, HTTPException, BackgroundTasks
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel, Field
    import uvicorn
    print("✅ FastAPI imports successful")
    
    print("3. Testing config import...")
    from config import settings
    print(f"✅ Config imported - log_level: {settings.log_level}")
    print(f"   Log file: {settings.log_file}")
    
    print("4. Testing log directory creation...")
    os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)
    print("✅ Log directory created")
    
    print("5. Testing logging configuration...")
    logging.basicConfig(
        level=getattr(logging, settings.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(settings.log_file),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger(__name__)
    logger.info("Test log message")
    print("✅ Logging configured successfully")
    
    print("6. Testing session manager import...")
    from session_manager import session_manager
    print("✅ Session manager imported")
    
    print("7. Testing browser manager import...")
    from browser_manager import browser_manager
    print("✅ Browser manager imported")
    
    print("8. Testing perplexity automation import...")
    from perplexity_automation import perplexity_automation
    print("✅ Perplexity automation imported")
    
    print("9. Testing FastAPI app creation...")
    
    @asynccontextmanager
    async def simple_lifespan(app: FastAPI):
        print("App starting...")
        yield
        print("App shutting down...")
    
    app = FastAPI(
        title="Test App",
        description="Test",
        version="1.0.0",
        lifespan=simple_lifespan
    )
    print("✅ FastAPI app created successfully")
    
    print("10. Testing uvicorn import and basic setup...")
    print(f"Uvicorn version: {uvicorn.__version__}")
    print("✅ All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("Full traceback:")
    traceback.print_exc()
    sys.exit(1)
